import json
from datetime import datetime
import structlog
from typing import Tuple, List, Dict, Any, Optional
import requests

from app.services.neo4j_service import execute_write_query, execute_read_query
from app.utils.redis.redis_service import RedisService
from app.utils.pinecone.pinecone_service import PineconeService
from app.utils.search.hybrid_search_engine import HybridSearchEngine
from app.utils.source_credentials import get_source_credentials
from app.modules.connectors.handlers.github.repository.github_queries import (
    GitHubUserQueries,
    GitHubRepositoryQueries,
    GitHubCodeFileQueries,
    GitHubRelationshipQueries,
    GitHubServiceAccountQueries,
    GitHubSyncQueries
)

logger = structlog.get_logger()

class GitHubService:
    """
    Service for GitHub integration operations.
    """

    def __init__(self):
        self.redis_service = RedisService()
        self.pinecone_service = PineconeService()
        
        # Initialize query instances (following GDrive pattern)
        self.user_queries = GitHubUserQueries()
        self.repository_queries = GitHubRepositoryQueries()
        self.file_queries = GitHubCodeFileQueries()
        self.relationship_queries = GitHubRelationshipQueries()
        self.service_account_queries = GitHubServiceAccountQueries()
        self.sync_queries = GitHubSyncQueries()
        
    def extract_repo_info_from_url(self, github_url: str) -> Optional[Dict[str, str]]:
        """
        Extract repository owner and name from a GitHub URL.
        
        Args:
            github_url: The GitHub URL
            
        Returns:
            Dictionary with owner and repo name or None if extraction failed
        """
        try:
            # Handle different URL formats
            # Format 1: https://github.com/owner/repo
            # Format 2: https://github.com/owner/repo/issues/123
            # Format 3: https://github.com/owner/repo/pull/456
            
            if "github.com/" in github_url:
                # Extract owner/repo from URL
                parts = github_url.split("github.com/")[1].split("/")
                if len(parts) >= 2:
                    owner = parts[0]
                    repo = parts[1]
                    return {"owner": owner, "repo": repo}
            
            logger.error(f"Unsupported GitHub URL format: {github_url}")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting repo info from URL: {str(e)}")
            return None

    def sync_repository_by_url(self,
                              github_url: str,
                              agent_id: str,
                              user_id: Optional[str],
                              organisation_id: str,
                              full_sync: bool = False) -> Tuple[bool, str, int]:
        """
        Sync a specific GitHub repository by URL.
        
        Args:
            github_url: GitHub repository URL
            agent_id: Agent ID performing the sync
            user_id: User ID (optional)
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync
            
        Returns:
            Tuple of (success, message, items_synced)
        """
        try:
            logger.info(f"Starting GitHub repository sync for URL: {github_url}")
            
            # Extract repository info from URL
            repo_info = self.extract_repo_info_from_url(github_url)
            if not repo_info:
                return False, "Failed to extract repository information from URL", 0
            
            owner = repo_info["owner"]
            repo_name = repo_info["repo"]
            
            # Get GitHub credentials
            credentials = get_source_credentials(organisation_id, 'github')
            if not credentials:
                return False, "No GitHub credentials found for organisation", 0
            
            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                return False, "GitHub token not found in credentials", 0
            
            # Create session with authentication
            session = requests.Session()
            session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'RuhOrg-GitHub-Connector/1.0'
            })
            
            # Sync repository data
            items_synced = 0
            
            # 1. Sync repository metadata
            repo_data = self._fetch_repository_data(session, owner, repo_name)
            if repo_data:
                self._create_or_update_repository(repo_data, organisation_id)
                items_synced += 1
                
                # 2. Sync issues if full sync
                if full_sync:
                    issues = self._fetch_repository_issues(session, owner, repo_name)
                    for issue in issues:
                        self._create_or_update_issue(issue, organisation_id, repo_data['id'])
                        items_synced += 1
                
                # 3. Sync pull requests if full sync
                if full_sync:
                    pull_requests = self._fetch_repository_pull_requests(session, owner, repo_name)
                    for pr in pull_requests:
                        self._create_or_update_pull_request(pr, organisation_id, repo_data['id'])
                        items_synced += 1
                
                # 4. Sync recent commits
                commits = self._fetch_repository_commits(session, owner, repo_name, limit=50)
                for commit in commits:
                    self._create_or_update_commit(commit, organisation_id, repo_data['id'])
                    items_synced += 1
                
                # 5. Create organizational relationships
                self._create_organizational_relationships(repo_data['id'], organisation_id)
                
                logger.info(f"Successfully synced GitHub repository: {owner}/{repo_name}, items: {items_synced}")
                return True, f"Successfully synced repository {owner}/{repo_name}", items_synced
            else:
                return False, f"Failed to fetch repository data for {owner}/{repo_name}", 0
                
        except Exception as e:
            logger.error(f"Error syncing GitHub repository: {str(e)}")
            return False, f"Sync failed: {str(e)}", 0

    def _fetch_repository_data(self, session: requests.Session, owner: str, repo: str) -> Optional[Dict]:
        """Fetch repository data from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}"
            response = session.get(url)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository data: {str(e)}")
            return None

    def _fetch_repository_issues(self, session: requests.Session, owner: str, repo: str, limit: int = 100) -> List[Dict]:
        """Fetch repository issues from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/issues"
            params = {'state': 'all', 'per_page': min(limit, 100)}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository issues: {str(e)}")
            return []

    def _fetch_repository_pull_requests(self, session: requests.Session, owner: str, repo: str, limit: int = 100) -> List[Dict]:
        """Fetch repository pull requests from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/pulls"
            params = {'state': 'all', 'per_page': min(limit, 100)}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository pull requests: {str(e)}")
            return []

    def _fetch_repository_commits(self, session: requests.Session, owner: str, repo: str, limit: int = 50) -> List[Dict]:
        """Fetch repository commits from GitHub API."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/commits"
            params = {'per_page': min(limit, 100)}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repository commits: {str(e)}")
            return []

    def _create_or_update_repository(self, repo_data: Dict, organisation_id: str):
        """Create or update repository in Neo4j following GDrive's folder creation patterns."""
        try:
            params = {
                'id': repo_data['id'],
                'organisation_id': organisation_id,
                'node_id': repo_data.get('node_id'),
                'name': repo_data.get('name'),
                'full_name': repo_data.get('full_name'),
                'html_url': repo_data.get('html_url'),
                'description': repo_data.get('description'),
                'private': repo_data.get('private', False),
                'fork': repo_data.get('fork', False),
                'archived': repo_data.get('archived', False),
                'language': repo_data.get('language'),
                'stargazers_count': repo_data.get('stargazers_count', 0),
                'watchers_count': repo_data.get('watchers_count', 0),
                'forks_count': repo_data.get('forks_count', 0),
                'open_issues_count': repo_data.get('open_issues_count', 0),
                'default_branch': repo_data.get('default_branch'),
                'created_at': repo_data.get('created_at'),
                'updated_at': repo_data.get('updated_at'),
                'pushed_at': repo_data.get('pushed_at'),
                'license': repo_data.get('license', {}).get('spdx_id') if repo_data.get('license') else None,
                'is_top_level': True  # Following GDrive's top-level folder pattern
            }

            # Use the new schema-based repository creation (following GDrive pattern)
            execute_write_query(self.repository_queries.CREATE_OR_UPDATE_REPOSITORY, params)

            # Create owner relationship if owner data exists (mirroring GDrive's owner pattern)
            if repo_data.get('owner'):
                self._create_or_update_user(repo_data['owner'], organisation_id)
                self._create_repository_owner_relationship(
                    repo_data['owner']['id'],
                    repo_data['id'],
                    organisation_id
                )

            # Create organizational relationships (exact GDrive pattern)
            self._create_organizational_relationships(repo_data['id'], organisation_id)

        except Exception as e:
            logger.error(f"Error creating/updating repository: {str(e)}")

    def _create_or_update_github_organization(self, org_data: Dict, organisation_id: str):
        """Create or update GitHub organization node (separate from generic Organization)."""
        try:
            params = {
                'id': org_data['id'],
                'organisation_id': organisation_id,
                'node_id': org_data.get('node_id'),
                'login': org_data.get('login'),
                'name': org_data.get('name'),
                'description': org_data.get('description'),
                'html_url': org_data.get('html_url'),
                'repo_count': org_data.get('public_repos', 0),
                'user_count': org_data.get('public_members', 0),
                'visibility': 'public' if org_data.get('public_repos', 0) > 0 else 'private',
                'github_id': str(org_data['id']),
                'created_at': org_data.get('created_at'),
                'updated_at': org_data.get('updated_at')
            }

            # Create GitHubOrganization node
            create_github_org_query = f"""
            MERGE (go:{self.organizational_queries.github_organization_label} {{id: $id}})
            SET go.organisation_id = $organisation_id,
                go.node_id = $node_id,
                go.login = $login,
                go.name = $name,
                go.description = $description,
                go.html_url = $html_url,
                go.repo_count = $repo_count,
                go.user_count = $user_count,
                go.visibility = $visibility,
                go.github_id = $github_id,
                go.created_at = $created_at,
                go.updated_at = $updated_at
            RETURN go
            """

            execute_write_query(create_github_org_query, params)
            logger.info(f"Created/updated GitHub organization: {org_data.get('login')}")

        except Exception as e:
            logger.error(f"Error creating/updating GitHub organization: {str(e)}")

    def _create_or_update_user(self, user_data: Dict, organisation_id: str):
        """Create or update GitHub user in Neo4j following GDrive's user creation patterns."""
        try:
            params = {
                'id': user_data['id'],
                'organisation_id': organisation_id,
                'node_id': user_data.get('node_id'),
                'login': user_data.get('login'),
                'name': user_data.get('name'),
                'email': user_data.get('email'),
                'html_url': user_data.get('html_url'),
                'type': user_data.get('type'),
                'site_admin': user_data.get('site_admin', False),
                'created_at': user_data.get('created_at'),
                'updated_at': user_data.get('updated_at'),
                'creation_type': 'auto_created'  # Following GDrive's user creation pattern
            }

            # Use the new schema-based user creation (following GDrive pattern)
            execute_write_query(self.user_queries.CREATE_OR_FIND_USER_BY_EMAIL, params)

            # Create organizational relationships for the user (mirroring GDrive's pattern)
            self._create_github_user_organizational_relationships(user_data, organisation_id)

        except Exception as e:
            logger.error(f"Error creating/updating GitHub user: {str(e)}")

    def _create_github_user_organizational_relationships(self, user_data: Dict, organisation_id: str):
        """
        Create organizational relationships for GitHub users following GDrive's exact pattern.
        This mirrors how GDrive creates User → Department → Organization relationships.
        """
        try:
            # If user has email, try to map to existing organizational User
            if user_data.get('email'):
                # Create mapping between organizational User and GitHubUser (mirroring GDrive pattern)
                mapping_params = {
                    'email': user_data['email'],
                    'github_login': user_data.get('login'),
                    'organisation_id': organisation_id,
                    'mapped_at': datetime.now().isoformat()
                }
                execute_write_query(self.relationship_queries.CREATE_USER_GITHUB_USER_MAPPING, mapping_params)

            # Ensure GitHub user has access through GENERAL department (exact GDrive pattern)
            general_access_params = {
                'github_user_id': user_data['id'],
                'organisation_id': organisation_id,
                'granted_at': datetime.now().isoformat()
            }
            execute_write_query(self.relationship_queries.MAP_GITHUB_USER_TO_GENERAL_DEPARTMENT, general_access_params)

        except Exception as e:
            logger.error(f"Error creating GitHub user organizational relationships: {str(e)}")

    def _create_or_update_issue(self, issue_data: Dict, organisation_id: str, repository_id: int):
        """Create or update GitHub issue in Neo4j."""
        try:
            params = {
                'id': issue_data['id'],
                'organisation_id': organisation_id,
                'node_id': issue_data.get('node_id'),
                'number': issue_data.get('number'),
                'html_url': issue_data.get('html_url'),
                'title': issue_data.get('title'),
                'body': issue_data.get('body'),
                'state': issue_data.get('state'),
                'labels': json.dumps([label.get('name') for label in issue_data.get('labels', [])]),
                'comments': issue_data.get('comments', 0),
                'created_at': issue_data.get('created_at'),
                'updated_at': issue_data.get('updated_at'),
                'closed_at': issue_data.get('closed_at')
            }
            
            execute_write_query(self.issue_queries.CREATE_ISSUE, params)
            
            # Create relationships
            if issue_data.get('user'):
                self._create_or_update_user(issue_data['user'], organisation_id)
                self._create_issue_creator_relationship(
                    issue_data['user']['id'],
                    issue_data['id'],
                    organisation_id
                )
            
            self._create_issue_repository_relationship(issue_data['id'], repository_id, organisation_id)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub issue: {str(e)}")

    def _create_or_update_pull_request(self, pr_data: Dict, organisation_id: str, repository_id: int):
        """Create or update GitHub pull request in Neo4j."""
        try:
            params = {
                'id': pr_data['id'],
                'organisation_id': organisation_id,
                'node_id': pr_data.get('node_id'),
                'number': pr_data.get('number'),
                'html_url': pr_data.get('html_url'),
                'title': pr_data.get('title'),
                'body': pr_data.get('body'),
                'state': pr_data.get('state'),
                'draft': pr_data.get('draft', False),
                'merged': pr_data.get('merged', False),
                'mergeable': pr_data.get('mergeable'),
                'additions': pr_data.get('additions', 0),
                'deletions': pr_data.get('deletions', 0),
                'changed_files': pr_data.get('changed_files', 0),
                'created_at': pr_data.get('created_at'),
                'updated_at': pr_data.get('updated_at'),
                'closed_at': pr_data.get('closed_at'),
                'merged_at': pr_data.get('merged_at')
            }
            
            execute_write_query(self.pull_request_queries.CREATE_PULL_REQUEST, params)
            
            # Create relationships
            if pr_data.get('user'):
                self._create_or_update_user(pr_data['user'], organisation_id)
                self._create_pull_request_creator_relationship(
                    pr_data['user']['id'],
                    pr_data['id'],
                    organisation_id
                )
            
            self._create_pull_request_repository_relationship(pr_data['id'], repository_id, organisation_id)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub pull request: {str(e)}")

    def _create_or_update_commit(self, commit_data: Dict, organisation_id: str, repository_id: int):
        """Create or update GitHub commit in Neo4j."""
        try:
            params = {
                'sha': commit_data['sha'],
                'organisation_id': organisation_id,
                'node_id': commit_data.get('node_id'),
                'html_url': commit_data.get('html_url'),
                'message': commit_data.get('commit', {}).get('message'),
                'additions': commit_data.get('stats', {}).get('additions', 0),
                'deletions': commit_data.get('stats', {}).get('deletions', 0),
                'total': commit_data.get('stats', {}).get('total', 0),
                'created_at': commit_data.get('commit', {}).get('author', {}).get('date')
            }
            
            execute_write_query(self.commit_queries.CREATE_COMMIT, params)
            
            # Create relationships
            if commit_data.get('author'):
                self._create_or_update_user(commit_data['author'], organisation_id)
                self._create_commit_author_relationship(
                    commit_data['author']['id'],
                    commit_data['sha'],
                    organisation_id
                )
            
            self._create_commit_repository_relationship(commit_data['sha'], repository_id, organisation_id)
            
        except Exception as e:
            logger.error(f"Error creating/updating GitHub commit: {str(e)}")

    def _create_repository_owner_relationship(self, user_id: int, repository_id: int, organisation_id: str):
        """Create repository owner relationship."""
        try:
            params = {
                'user_id': user_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.repository_queries.CREATE_REPOSITORY_OWNER_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating repository owner relationship: {str(e)}")

    def _create_issue_creator_relationship(self, user_id: int, issue_id: int, organisation_id: str):
        """Create issue creator relationship."""
        try:
            params = {
                'user_id': user_id,
                'issue_id': issue_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.issue_queries.CREATE_ISSUE_CREATOR_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating issue creator relationship: {str(e)}")

    def _create_issue_repository_relationship(self, issue_id: int, repository_id: int, organisation_id: str):
        """Create issue repository relationship."""
        try:
            params = {
                'issue_id': issue_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.issue_queries.CREATE_ISSUE_REPOSITORY_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating issue repository relationship: {str(e)}")

    def _create_pull_request_creator_relationship(self, user_id: int, pull_request_id: int, organisation_id: str):
        """Create pull request creator relationship."""
        try:
            params = {
                'user_id': user_id,
                'pull_request_id': pull_request_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.pull_request_queries.CREATE_PULL_REQUEST_CREATOR_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating pull request creator relationship: {str(e)}")

    def _create_pull_request_repository_relationship(self, pull_request_id: int, repository_id: int, organisation_id: str):
        """Create pull request repository relationship."""
        try:
            params = {
                'pull_request_id': pull_request_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.pull_request_queries.CREATE_PULL_REQUEST_REPOSITORY_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating pull request repository relationship: {str(e)}")

    def _create_commit_author_relationship(self, user_id: int, commit_sha: str, organisation_id: str):
        """Create commit author relationship."""
        try:
            params = {
                'user_id': user_id,
                'commit_sha': commit_sha,
                'organisation_id': organisation_id,
                'authored_at': datetime.now().isoformat()
            }
            execute_write_query(self.commit_queries.CREATE_COMMIT_AUTHOR_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating commit author relationship: {str(e)}")

    def _create_commit_repository_relationship(self, commit_sha: str, repository_id: int, organisation_id: str):
        """Create commit repository relationship."""
        try:
            params = {
                'commit_sha': commit_sha,
                'repository_id': repository_id,
                'organisation_id': organisation_id,
                'created_at': datetime.now().isoformat()
            }
            execute_write_query(self.commit_queries.CREATE_COMMIT_REPOSITORY_RELATIONSHIP, params)
        except Exception as e:
            logger.error(f"Error creating commit repository relationship: {str(e)}")

    def _create_organizational_relationships(self, repository_id: int, organisation_id: str):
        """Create relationships between GitHub repository and organizational entities following GDrive patterns."""
        try:
            # Create organisation-repository relationship (mirroring GDrive's Organization → GoogleDriveFolder pattern)
            params = {
                'organisation_id': organisation_id,
                'repository_id': repository_id,
                'granted_at': datetime.now().isoformat()
            }
            execute_write_query(self.relationship_queries.CREATE_ORGANISATION_REPOSITORY_RELATIONSHIP, params)

            # Create department-repository relationships for all departments (mirroring GDrive's department access)
            execute_write_query(self.relationship_queries.CREATE_DEPARTMENT_REPOSITORY_RELATIONSHIP, params)

            # Create user-repository relationships through departments (mirroring GDrive's user access pattern)
            execute_write_query(self.relationship_queries.MAP_USER_REPOSITORY_ACCESS, params)

            # Ensure GENERAL department has access (exact GDrive pattern)
            execute_write_query(self.relationship_queries.MAP_GENERAL_DEPARTMENT_ACCESS, params)

        except Exception as e:
            logger.error(f"Error creating organizational relationships: {str(e)}")

    def create_github_source_node(self, organisation_id: str, source_name: str = "GitHub") -> tuple[bool, str]:
        """
        Create GitHub Source node and establish Organization → Source relationship.
        Mirrors GDrive's source creation pattern exactly.

        Args:
            organisation_id: The organization ID
            source_name: Name for the GitHub source

        Returns:
            Tuple containing success status and message
        """
        try:
            import uuid
            from datetime import datetime

            # Check if GitHub source already exists (following GDrive's pattern)
            existing_source_query = """
            MATCH (o:Organisation {id: $organisation_id})-[:HAS_SOURCE]->(s:Source {type: 'github'})
            RETURN s
            """

            existing_result = execute_read_query(existing_source_query, {'organisation_id': organisation_id})
            if existing_result:
                logger.info(f"GitHub source already exists for organisation: {organisation_id}")
                return True, "GitHub source already exists"

            # Create Source node following GDrive's exact pattern
            source_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()

            # Use the comprehensive organizational relationship creation query
            params = {
                'organisation_id': organisation_id,
                'source_id': source_id,
                'name': source_name,
                'type': 'github',  # Following GDrive's source type pattern
                'is_validated': True,
                'validation_message': 'GitHub source created successfully',
                'last_validated_at': current_time,
                'created_at': current_time,
                'updated_at': current_time,
                'granted_at': current_time
            }

            result = execute_write_query(
                self.organizational_queries.CREATE_GITHUB_SOURCE_ORGANIZATIONAL_RELATIONSHIPS,
                params
            )

            if result:
                logger.info(f"Successfully created GitHub Source node for organisation: {organisation_id}")
                return True, f"GitHub source created successfully with ID: {source_id}"
            else:
                return False, "Failed to create GitHub source node"

        except Exception as e:
            logger.error(f"Error creating GitHub source node: {str(e)}")
            return False, f"Error creating GitHub source: {str(e)}"

    def sync_organizational_access_patterns(self, organisation_id: str):
        """
        Sync organizational access patterns following GDrive patterns.
        This ensures all GitHub entities have proper organizational relationships.
        """
        try:
            logger.info(f"Syncing organizational access patterns for organisation: {organisation_id}")

            params = {
                'organisation_id': organisation_id,
                'granted_at': datetime.now().isoformat()
            }

            # Use the comprehensive organizational sync query
            result = execute_write_query(
                self.organizational_queries.SYNC_ORGANIZATIONAL_ACCESS_PATTERNS,
                params
            )

            if result:
                org_count = result[0].get('org_access_count', 0)
                dept_count = result[0].get('dept_access_count', 0)
                user_count = result[0].get('user_access_count', 0)

                logger.info(f"Organizational access sync completed: {org_count} org relationships, "
                           f"{dept_count} department relationships, {user_count} user relationships")

        except Exception as e:
            logger.error(f"Error syncing organizational access patterns: {str(e)}")

    def map_github_users_to_org_users(self, organisation_id: str):
        """
        Map GitHub users to organizational users based on email matching.
        """
        try:
            logger.info(f"Mapping GitHub users to organizational users for organisation: {organisation_id}")

            params = {
                'organisation_id': organisation_id,
                'mapped_at': datetime.now().isoformat()
            }

            result = execute_write_query(
                self.organizational_queries.MAP_GITHUB_USERS_TO_ORG_USERS,
                params
            )

            if result:
                mappings_created = result[0].get('mappings_created', 0)
                logger.info(f"Created {mappings_created} GitHub user to organizational user mappings")
                return mappings_created

            return 0

        except Exception as e:
            logger.error(f"Error mapping GitHub users to organizational users: {str(e)}")
            return 0

    def check_user_repository_access(self, user_id: str, repository_id: int, organisation_id: str) -> Dict[str, Any]:
        """
        Check if a user has access to a GitHub repository through organizational hierarchy.
        """
        try:
            params = {
                'user_id': user_id,
                'repository_id': repository_id,
                'organisation_id': organisation_id
            }

            result = execute_read_query(
                self.organizational_queries.CHECK_USER_REPOSITORY_ACCESS,
                params
            )

            if result:
                return result[0]

            return {'has_access': False}

        except Exception as e:
            logger.error(f"Error checking user repository access: {str(e)}")
            return {'has_access': False, 'error': str(e)}

    def get_user_accessible_repositories(self, user_id: str, organisation_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get repositories accessible to a user through organizational hierarchy.
        """
        try:
            params = {
                'user_id': user_id,
                'organisation_id': organisation_id,
                'limit': limit
            }

            result = execute_read_query(
                self.organizational_queries.GET_USER_ACCESSIBLE_REPOSITORIES,
                params
            )

            return result if result else []

        except Exception as e:
            logger.error(f"Error getting user accessible repositories: {str(e)}")
            return []

    def get_department_accessible_repositories(self, department_id: str, organisation_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get repositories accessible to a department.
        """
        try:
            params = {
                'department_id': department_id,
                'organisation_id': organisation_id,
                'limit': limit
            }

            result = execute_read_query(
                self.organizational_queries.GET_DEPARTMENT_ACCESSIBLE_REPOSITORIES,
                params
            )

            return result if result else []

        except Exception as e:
            logger.error(f"Error getting department accessible repositories: {str(e)}")
            return []

    def sync_github(self, organisation_id: str, full_sync: bool = False) -> Tuple[bool, str, int, int]:
        """
        Perform a full sync of GitHub data for an organisation.
        
        Args:
            organisation_id: Organisation ID
            full_sync: Whether to perform full sync
            
        Returns:
            Tuple of (success, message, repositories_synced, total_items_synced)
        """
        try:
            logger.info(f"Starting GitHub sync for organisation: {organisation_id}")

            # Step 1: Create GitHub Source node (mirroring GDrive's source creation pattern)
            source_success, source_message = self.create_github_source_node(organisation_id)
            if not source_success:
                logger.warning(f"GitHub source creation warning: {source_message}")

            # Get GitHub credentials
            credentials = get_source_credentials(organisation_id, 'github')
            if not credentials:
                return False, "No GitHub credentials found for organisation", 0, 0

            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                return False, "GitHub token not found in credentials", 0, 0
            
            # Create session with authentication
            session = requests.Session()
            session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'RuhOrg-GitHub-Connector/1.0'
            })
            
            repositories_synced = 0
            total_items_synced = 0

            # Fetch and sync GitHub organizations first
            github_orgs = self._fetch_github_organizations(session)
            for org_data in github_orgs:
                try:
                    self._create_or_update_github_organization(org_data, organisation_id)
                    total_items_synced += 1
                except Exception as e:
                    logger.error(f"Error syncing GitHub organization {org_data.get('login', 'unknown')}: {str(e)}")
                    continue

            # Fetch user's repositories
            repositories = self._fetch_user_repositories(session)
            
            for repo_data in repositories:
                try:
                    # Sync repository
                    self._create_or_update_repository(repo_data, organisation_id)
                    repositories_synced += 1
                    total_items_synced += 1
                    
                    if full_sync:
                        # Sync issues
                        issues = self._fetch_repository_issues(session, repo_data['owner']['login'], repo_data['name'])
                        for issue in issues:
                            self._create_or_update_issue(issue, organisation_id, repo_data['id'])
                            total_items_synced += 1
                        
                        # Sync pull requests
                        pull_requests = self._fetch_repository_pull_requests(session, repo_data['owner']['login'], repo_data['name'])
                        for pr in pull_requests:
                            self._create_or_update_pull_request(pr, organisation_id, repo_data['id'])
                            total_items_synced += 1
                    
                    # Sync recent commits
                    commits = self._fetch_repository_commits(session, repo_data['owner']['login'], repo_data['name'], limit=20)
                    for commit in commits:
                        self._create_or_update_commit(commit, organisation_id, repo_data['id'])
                        total_items_synced += 1
                    
                    # Create organizational relationships
                    self._create_organizational_relationships(repo_data['id'], organisation_id)
                    
                except Exception as e:
                    logger.error(f"Error syncing repository {repo_data.get('full_name', 'unknown')}: {str(e)}")
                    continue
            
            # Update last sync time
            self._update_last_sync_time(organisation_id)

            # Sync organizational access patterns (following GDrive pattern)
            self.sync_organizational_access_patterns(organisation_id)

            # Map GitHub entities to departments (mirroring GDrive's department mapping)
            self.map_github_entities_to_departments(organisation_id)

            # Map GitHub users to organizational users
            self.map_github_users_to_org_users(organisation_id)

            logger.info(f"GitHub sync completed: {repositories_synced} repositories, {total_items_synced} total items")
            return True, f"Successfully synced {repositories_synced} repositories. {source_message}", repositories_synced, total_items_synced
            
        except Exception as e:
            logger.error(f"Error during GitHub sync: {str(e)}")
            return False, f"Sync failed: {str(e)}", 0, 0

    def _fetch_github_organizations(self, session: requests.Session) -> List[Dict]:
        """Fetch GitHub organizations for the authenticated user."""
        try:
            url = "https://api.github.com/user/orgs"
            response = session.get(url)
            response.raise_for_status()

            organizations = []
            for org in response.json():
                # Fetch detailed organization info
                org_url = f"https://api.github.com/orgs/{org['login']}"
                org_response = session.get(org_url)
                if org_response.status_code == 200:
                    organizations.append(org_response.json())
                else:
                    # Use basic org info if detailed fetch fails
                    organizations.append(org)

            return organizations
        except Exception as e:
            logger.error(f"Failed to fetch GitHub organizations: {str(e)}")
            return []

    def _fetch_user_repositories(self, session: requests.Session) -> List[Dict]:
        """Fetch user's repositories from GitHub API."""
        try:
            url = "https://api.github.com/user/repos"
            params = {'type': 'all', 'sort': 'updated', 'per_page': 100}
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch user repositories: {str(e)}")
            return []

    def map_github_entities_to_departments(self, organisation_id: str):
        """
        Map GitHub entities to departments following GDrive's department access patterns.
        This mirrors how GDrive maps folders and files to departments.
        """
        try:
            logger.info(f"Mapping GitHub entities to departments for organisation: {organisation_id}")

            params = {
                'organisation_id': organisation_id,
                'granted_at': datetime.now().isoformat()
            }

            # Use the comprehensive department mapping query
            result = execute_write_query(
                self.organizational_queries.MAP_GITHUB_ENTITIES_TO_DEPARTMENTS,
                params
            )

            if result and len(result) > 0:
                repo_mappings = result[0].get('repo_mappings', 0)
                issue_mappings = result[0].get('issue_mappings', 0)
                pr_mappings = result[0].get('pr_mappings', 0)

                logger.info(f"Successfully mapped GitHub entities to departments: "
                          f"{repo_mappings} repositories, {issue_mappings} issues, {pr_mappings} pull requests")
            else:
                logger.info("No GitHub entities found to map to departments")

        except Exception as e:
            logger.error(f"Error mapping GitHub entities to departments: {str(e)}")

    def map_github_users_to_org_users(self, organisation_id: str):
        """
        Map GitHub users to organizational users following GDrive's user mapping patterns.
        This creates relationships between GitHubUser nodes and organizational User nodes.
        """
        try:
            logger.info(f"Mapping GitHub users to organizational users for organisation: {organisation_id}")

            # Get all GitHub users for this organization
            github_users_query = """
            MATCH (gu:GitHubUser {organisation_id: $organisation_id})
            WHERE gu.email IS NOT NULL
            RETURN gu.id as github_user_id, gu.login as github_login, gu.email as email
            """

            github_users = execute_read_query(github_users_query, {'organisation_id': organisation_id})

            mapped_count = 0
            for github_user in github_users:
                try:
                    # Try to map to existing organizational user by email
                    mapping_params = {
                        'email': github_user['email'],
                        'github_login': github_user['github_login'],
                        'organisation_id': organisation_id,
                        'mapped_at': datetime.now().isoformat()
                    }

                    result = execute_write_query(
                        self.relationship_queries.CREATE_USER_GITHUB_USER_MAPPING,
                        mapping_params
                    )

                    if result:
                        mapped_count += 1

                except Exception as e:
                    logger.warning(f"Failed to map GitHub user {github_user['github_login']}: {str(e)}")
                    continue

            logger.info(f"Successfully mapped {mapped_count} GitHub users to organizational users")

        except Exception as e:
            logger.error(f"Error mapping GitHub users to organizational users: {str(e)}")

    def sync_github_incremental(self, organisation_id: str, since_date: str = None) -> Tuple[bool, str, int, int]:
        """
        Perform incremental sync of GitHub data following GDrive's incremental sync patterns.

        Args:
            organisation_id: The organization ID
            since_date: ISO date string for incremental sync (if None, uses last sync time)

        Returns:
            Tuple containing success status, message, repositories synced, and total items synced
        """
        try:
            logger.info(f"Starting GitHub incremental sync for organisation: {organisation_id}")

            # Get last sync time if since_date not provided (mirroring GDrive pattern)
            if not since_date:
                since_date = self._get_last_sync_time(organisation_id)
                if not since_date:
                    logger.info("No previous sync found, performing full sync")
                    return self.sync_github(organisation_id, full_sync=True)

            # Get GitHub credentials
            credentials = get_source_credentials(organisation_id, 'github')
            if not credentials:
                return False, "No GitHub credentials found for organisation", 0, 0

            token = credentials.get('token') or credentials.get('access_token')
            if not token:
                return False, "GitHub token not found in credentials", 0, 0

            # Create session with authentication
            session = requests.Session()
            session.headers.update({
                'Authorization': f'token {token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'RuhOrg-GitHub-Connector/1.0'
            })

            repositories_synced = 0
            total_items_synced = 0

            # Fetch repositories updated since last sync
            repositories = self._fetch_repositories_since(session, since_date)

            for repo_data in repositories:
                try:
                    # Check if repository was updated since last sync
                    if self._is_repository_updated_since(repo_data, since_date):
                        self._create_or_update_repository(repo_data, organisation_id)
                        repositories_synced += 1
                        total_items_synced += 1

                        # Sync recent activity (issues, PRs, commits) for updated repositories
                        recent_items = self._sync_repository_recent_activity(
                            session, repo_data, organisation_id, since_date
                        )
                        total_items_synced += recent_items

                except Exception as e:
                    logger.error(f"Error in incremental sync for repository {repo_data.get('full_name', 'unknown')}: {str(e)}")
                    continue

            # Update last sync time
            self._update_last_sync_time(organisation_id)

            # Sync organizational relationships for any new/updated entities
            self.sync_organizational_access_patterns(organisation_id)

            logger.info(f"GitHub incremental sync completed: {repositories_synced} repositories, {total_items_synced} total items")
            return True, f"Successfully synced {repositories_synced} updated repositories", repositories_synced, total_items_synced

        except Exception as e:
            logger.error(f"Error during GitHub incremental sync: {str(e)}")
            return False, f"Incremental sync failed: {str(e)}", 0, 0

    def _get_last_sync_time(self, organisation_id: str) -> Optional[str]:
        """Get the last sync time for the organisation (mirroring GDrive pattern)."""
        try:
            query = """
            MATCH (o:Organisation {id: $organisation_id})-[:HAS_SOURCE]->(s:Source {type: 'github'})
            RETURN s.last_sync_time as last_sync_time
            """

            result = execute_read_query(query, {'organisation_id': organisation_id})
            if result and result[0].get('last_sync_time'):
                return result[0]['last_sync_time']
            return None

        except Exception as e:
            logger.error(f"Error getting last sync time: {str(e)}")
            return None

    def _fetch_repositories_since(self, session: requests.Session, since_date: str) -> List[Dict]:
        """Fetch repositories updated since the given date."""
        try:
            url = "https://api.github.com/user/repos"
            params = {
                'type': 'all',
                'sort': 'updated',
                'since': since_date,
                'per_page': 100
            }
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch repositories since {since_date}: {str(e)}")
            return []

    def _is_repository_updated_since(self, repo_data: Dict, since_date: str) -> bool:
        """Check if repository was updated since the given date."""
        try:
            from datetime import datetime
            repo_updated = datetime.fromisoformat(repo_data.get('updated_at', '').replace('Z', '+00:00'))
            since_datetime = datetime.fromisoformat(since_date.replace('Z', '+00:00'))
            return repo_updated > since_datetime
        except Exception as e:
            logger.warning(f"Error comparing dates for repository update check: {str(e)}")
            return True  # Default to sync if we can't determine

    def _sync_repository_recent_activity(self, session: requests.Session, repo_data: Dict,
                                       organisation_id: str, since_date: str) -> int:
        """Sync recent activity for a repository (issues, PRs, commits) since given date."""
        items_synced = 0

        try:
            owner = repo_data['owner']['login']
            repo_name = repo_data['name']

            # Sync recent issues
            recent_issues = self._fetch_repository_issues_since(session, owner, repo_name, since_date)
            for issue in recent_issues:
                self._create_or_update_issue(issue, organisation_id, repo_data['id'])
                items_synced += 1

            # Sync recent pull requests
            recent_prs = self._fetch_repository_pull_requests_since(session, owner, repo_name, since_date)
            for pr in recent_prs:
                self._create_or_update_pull_request(pr, organisation_id, repo_data['id'])
                items_synced += 1

            # Sync recent commits
            recent_commits = self._fetch_repository_commits_since(session, owner, repo_name, since_date)
            for commit in recent_commits:
                self._create_or_update_commit(commit, organisation_id, repo_data['id'])
                items_synced += 1

        except Exception as e:
            logger.error(f"Error syncing recent activity for repository {repo_data.get('full_name', 'unknown')}: {str(e)}")

        return items_synced

    def _update_last_sync_time(self, organisation_id: str):
        """Update the last sync time for the organisation (following GDrive's Source pattern)."""
        try:
            current_time = datetime.now().isoformat()
            params = {
                'organisation_id': organisation_id,
                'sync_time': current_time
            }
            execute_write_query(self.sync_queries.UPDATE_LAST_SYNC_TIME, params)
            logger.debug(f"Updated last sync time for organisation {organisation_id}: {current_time}")
        except Exception as e:
            logger.error(f"Error updating last sync time: {str(e)}")

    def _fetch_repository_issues_since(self, session: requests.Session, owner: str, repo: str, since_date: str) -> List[Dict]:
        """Fetch repository issues updated since the given date."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/issues"
            params = {
                'state': 'all',
                'since': since_date,
                'per_page': 100
            }
            response = session.get(url, params=params)
            response.raise_for_status()
            # Filter out pull requests (GitHub API includes PRs in issues endpoint)
            return [issue for issue in response.json() if 'pull_request' not in issue]
        except Exception as e:
            logger.error(f"Failed to fetch issues since {since_date} for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_repository_pull_requests_since(self, session: requests.Session, owner: str, repo: str, since_date: str) -> List[Dict]:
        """Fetch repository pull requests updated since the given date."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/pulls"
            params = {
                'state': 'all',
                'sort': 'updated',
                'direction': 'desc',
                'per_page': 100
            }
            response = session.get(url, params=params)
            response.raise_for_status()

            # Filter by date since GitHub API doesn't support 'since' parameter for PRs
            from datetime import datetime
            since_datetime = datetime.fromisoformat(since_date.replace('Z', '+00:00'))

            filtered_prs = []
            for pr in response.json():
                try:
                    pr_updated = datetime.fromisoformat(pr.get('updated_at', '').replace('Z', '+00:00'))
                    if pr_updated > since_datetime:
                        filtered_prs.append(pr)
                except Exception:
                    continue

            return filtered_prs
        except Exception as e:
            logger.error(f"Failed to fetch pull requests since {since_date} for {owner}/{repo}: {str(e)}")
            return []

    def _fetch_repository_commits_since(self, session: requests.Session, owner: str, repo: str, since_date: str) -> List[Dict]:
        """Fetch repository commits since the given date."""
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/commits"
            params = {
                'since': since_date,
                'per_page': 50  # Limit commits to avoid rate limiting
            }
            response = session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch commits since {since_date} for {owner}/{repo}: {str(e)}")
            return []

    def get_sync_statistics(self, organisation_id: str) -> Dict[str, Any]:
        """Get sync statistics for an organisation."""
        try:
            params = {'organisation_id': organisation_id}
            result = execute_read_query(self.sync_queries.GET_SYNC_STATISTICS, params)

            if result:
                return result[0]
            return {}
            
        except Exception as e:
            logger.error(f"Error getting sync statistics: {str(e)}")
            return {}

    def search_github_content(self, organisation_id: str, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search GitHub content using hybrid search.
        
        Args:
            organisation_id: Organisation ID
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of search results
        """
        try:
            # Use hybrid search engine for GitHub content
            search_engine = HybridSearchEngine()
            
            # Define GitHub entity types for search
            entity_types = [
                'GitHubRepository',
                'GitHubIssue', 
                'GitHubPullRequest',
                'GitHubCodeFile'
            ]
            
            results = search_engine.search(
                query=query,
                organisation_id=organisation_id,
                entity_types=entity_types,
                limit=limit
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching GitHub content: {str(e)}")
            return []

    def get_repository_statistics(self, organisation_id: str, repository_id: int) -> Dict[str, Any]:
        """Get statistics for a specific repository."""
        try:
            params = {
                'organisation_id': organisation_id,
                'repository_id': repository_id
            }
            result = execute_read_query(self.metadata_queries.GET_REPOSITORY_STATISTICS, params)

            if result:
                return result[0]
            return {}

        except Exception as e:
            logger.error(f"Error getting repository statistics: {str(e)}")
            return {}

    def get_user_activity_summary(self, organisation_id: str, user_id: int) -> Dict[str, Any]:
        """Get activity summary for a GitHub user."""
        try:
            params = {
                'organisation_id': organisation_id,
                'user_id': user_id
            }
            result = execute_read_query(self.metadata_queries.GET_USER_ACTIVITY_SUMMARY, params)

            if result:
                return result[0]
            return {}

        except Exception as e:
            logger.error(f"Error getting user activity summary: {str(e)}")
            return {}
