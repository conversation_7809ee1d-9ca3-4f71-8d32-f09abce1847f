"""
GitHub Connector Neo4j Queries

This module contains all Neo4j queries for the GitHub connector,
organized by entity type and functionality following GDrive patterns.
"""

from app.modules.connectors.handlers.github.models.schema_loader import github_schema
from app.modules.organisation.models.schema_loader import schema as organisation_schema
from app.modules.agents.models.schema_loader import agent_schema
import structlog

logger = structlog.get_logger()


class GitHubUserQueries:
    """Queries for managing GitHub users and their relationships with the application's users."""

    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.has_account_rel = "HAS_ACCOUNT"  # Custom relationship for linking users

    @property
    def CREATE_OR_UPDATE_GITHUB_USER(self):
        """Create or update a GitHub user and link it to an application user."""
        return f"""
        MERGE (gh_user:{self.github_user_label} {{id: $github_user_id}})
        ON CREATE SET
            gh_user.login = $login,
            gh_user.name = $name,
            gh_user.email = $email,
            gh_user.organisation_id = $organisation_id,
            gh_user.created_at = $created_at,
            gh_user.status = 'active'
        ON MATCH SET
            gh_user.login = $login,
            gh_user.name = $name,
            gh_user.email = $email,
            gh_user.updated_at = $updated_at
        WITH gh_user
        MATCH (user:{self.user_label} {{email: $email}})
        MERGE (user)-[:{self.has_account_rel}]->(gh_user)
        RETURN gh_user
        """

    @property
    def UPDATE_USER_DETAILS(self):
        """Update a GitHub user's details."""
        return f"""
        MATCH (gh_user:{self.github_user_label} {{id: $github_user_id}})
        SET gh_user.login = $login,
            gh_user.name = $name,
            gh_user.email = $email,
            gh_user.updated_at = $updated_at
        RETURN gh_user
        """

    @property
    def UPDATE_USER_STATUS(self):
        """Update the status of a GitHub user (e.g., to 'deactivated')."""
        return f"""
        MATCH (gh_user:{self.github_user_label} {{id: $github_user_id}})
        SET gh_user.status = $status,
            gh_user.updated_at = $updated_at
        RETURN gh_user
        """

class GitHubOrganizationQueries:
    """Queries for managing GitHub organizations."""

    def __init__(self):
        self.github_org_label = github_schema.get_node_labels()[2]  # GitHubOrganization
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.member_of_rel = "MEMBER_OF_ORG"  # From schema relationships
        self.belongs_to_org_rel = "ORG_OWNS_REPO"  # From schema relationships

    @property
    def CREATE_OR_UPDATE_GITHUB_ORGANIZATION(self):
        """Create or update a GitHub organization."""
        return f"""
        MERGE (gh_org:{self.github_org_label} {{id: $github_org_id}})
        ON CREATE SET
            gh_org.login = $login,
            gh_org.name = $name,
            gh_org.description = $description,
            gh_org.html_url = $html_url,
            gh_org.organisation_id = $organisation_id,
            gh_org.created_at = $created_at
        ON MATCH SET
            gh_org.login = $login,
            gh_org.name = $name,
            gh_org.description = $description,
            gh_org.html_url = $html_url,
            gh_org.updated_at = $updated_at
        RETURN gh_org
        """

    @property
    def UPDATE_ORGANIZATION_STATS(self):
        """Update a GitHub organization's statistics."""
        return f"""
        MATCH (gh_org:{self.github_org_label} {{id: $github_org_id}})
        SET gh_org.repo_count = $repo_count,
            gh_org.user_count = $user_count,
            gh_org.updated_at = $updated_at
        RETURN gh_org
        """

    @property
    def ADD_MEMBER_TO_ORGANIZATION(self):
        """Add a user as a member of a GitHub organization."""
        return f"""
        MATCH (gh_user:{self.github_user_label} {{id: $github_user_id}})
        MATCH (gh_org:{self.github_org_label} {{id: $github_org_id}})
        MERGE (gh_user)-[r:{self.member_of_rel}]->(gh_org)
        SET r.role = $role, r.join_date = $join_date
        RETURN r
        """

    @property
    def REMOVE_MEMBER_FROM_ORGANIZATION(self):
        """Remove a user from a GitHub organization."""
        return f"""
        MATCH (gh_user:{self.github_user_label} {{id: $github_user_id}})-[r:{self.member_of_rel}]->(gh_org:{self.github_org_label} {{id: $github_org_id}})
        DELETE r
        """

    @property
    def LIST_ORGANIZATION_MEMBERS(self):
        """List all members of a GitHub organization."""
        return f"""
        MATCH (gh_user:{self.github_user_label})-[r:{self.member_of_rel}]->(gh_org:{self.github_org_label} {{id: $github_org_id}})
        RETURN gh_user, r.role as role
        """

    @property
    def LIST_ORGANIZATION_REPOSITORIES(self):
        """List all repositories of a GitHub organization."""
        return f"""
        MATCH (repo:{self.repository_label})-[:{self.belongs_to_org_rel}]->(gh_org:{self.github_org_label} {{id: $github_org_id}})
        RETURN repo
        """

class GitHubRepositoryQueries:
    """Queries for GitHub repository management."""

    def __init__(self):
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.github_org_label = github_schema.get_node_labels()[2]  # GitHubOrganization
        self.pr_label = github_schema.get_node_labels()[5]  # GitHubPullRequest
        self.branch_label = github_schema.get_node_labels()[7]  # GitHubBranch
        self.has_access_rel = "CONTRIBUTES_TO"  # From schema relationships
        self.belongs_to_org_rel = "ORG_OWNS_REPO"  # From schema relationships
        self.belongs_to_repo_rel = "BELONGS_TO"  # From schema relationships
        self.has_branch_rel = "HAS_BRANCH"  # Custom relationship for branches

    @property
    def CREATE_OR_UPDATE_REPOSITORY(self):
        """Create or update a repository and link it to its organization."""
        return f"""
        MERGE (repo:{self.repository_label} {{id: $repository_id}})
        SET repo.organisation_id = $organisation_id,
            repo.name = $name,
            repo.full_name = $full_name,
            repo.private = $private,
            repo.html_url = $html_url,
            repo.description = $description,
            repo.updated_at = $updated_at
        WITH repo
        MATCH (gh_org:{self.github_org_label} {{id: $github_org_id}})
        MERGE (repo)-[:{self.belongs_to_org_rel}]->(gh_org)
        RETURN repo
        """

    @property
    def UPDATE_REPOSITORY_DETAILS(self):
        """Update a repository's core details."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        SET repo.name = $name,
            repo.description = $description,
            repo.default_branch = $default_branch,
            repo.updated_at = $updated_at
        RETURN repo
        """

    @property
    def UPDATE_REPOSITORY_STATUS(self):
        """Update a repository's status (archived, private)."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        SET repo.archived = $archived,
            repo.private = $private,
            repo.updated_at = $updated_at
        RETURN repo
        """

    @property
    def UPDATE_REPOSITORY_STATS(self):
        """Update a repository's statistics."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        SET repo.stargazers_count = $stargazers_count,
            repo.watchers_count = $watchers_count,
            repo.forks_count = $forks_count,
            repo.open_issues_count = $open_issues_count,
            repo.updated_at = $updated_at
        RETURN repo
        """

    @property
    def ADD_COLLABORATOR(self):
        """Add a collaborator to a repository with a specific role."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MATCH (gh_user:{self.github_user_label} {{id: $github_user_id}})
        MERGE (gh_user)-[r:{self.has_access_rel}]->(repo)
        SET r.role = $role,
            r.granted_at = $granted_at
        RETURN r
        """

    @property
    def REMOVE_COLLABORATOR(self):
        """Remove a collaborator's access to a repository."""
        return f"""
        MATCH (gh_user:{self.github_user_label} {{id: $github_user_id}})-[r:{self.has_access_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        DELETE r
        """

    @property
    def UPDATE_COLLABORATOR_ROLE(self):
        """Update a collaborator's role on a repository."""
        return f"""
        MATCH (gh_user:{self.github_user_label} {{id: $github_user_id}})-[r:{self.has_access_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        SET r.role = $new_role
        RETURN r
        """

    @property
    def LIST_REPOSITORY_COLLABORATORS(self):
        """List all collaborators of a repository."""
        return f"""
        MATCH (gh_user:{self.github_user_label})-[r:{self.has_access_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN gh_user, r.role as role
        """

    @property
    def LIST_REPOSITORY_PULL_REQUESTS(self):
        """List all pull requests in a repository."""
        return f"""
        MATCH (pr:{self.pr_label})-[:{self.belongs_to_repo_rel}]->(repo:{self.repository_label} {{id: $repository_id}})
        RETURN pr
        """

    @property
    def LIST_REPOSITORY_BRANCHES(self):
        """List all branches in a repository."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})-[:{self.has_branch_rel}]->(b:{self.branch_label})
        RETURN b
        """

    @property
    def DELETE_REPOSITORY(self):
        """Delete a single repository by its ID."""
        return f"""
        MATCH (r:{self.repository_label} {{id: $repository_id}})
        DETACH DELETE r
        """

    @property
    def DELETE_ORGANIZATION_REPOSITORIES(self):
        """Delete all repositories for an organization."""
        return f"""
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        DETACH DELETE r
        """







class GitHubCodeFileQueries:
    """Queries for GitHub code file management."""

    def __init__(self):
        self.file_label = github_schema.get_node_labels()[3]  # GitHubCodeFile
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.contains_file_rel = "BELONGS_TO"  # From schema relationships

    @property
    def CREATE_OR_UPDATE_FILE(self):
        """Create or update a code file."""
        return f"""
        MERGE (file:{self.file_label} {{path: $path, repository_id: $repository_id}})
        SET file.sha = $sha,
            file.name = $name,
            file.content = $content,
            file.size = $size,
            file.html_url = $html_url,
            file.download_url = $download_url,
            file.updated_at = $updated_at
        WITH file
        MATCH (repo:{self.repository_label} {{id: $repository_id}})
        MERGE (repo)-[:{self.contains_file_rel}]->(file)
        RETURN file
        """

    @property
    def COUNT_FILES_IN_REPOSITORY(self):
        """Count files in a repository."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})-[:{self.contains_file_rel}]->(file:{self.file_label})
        RETURN count(file) as total_count
        """

    @property
    def LIST_FILES_IN_REPOSITORY(self):
        """List all files in a repository."""
        return f"""
        MATCH (repo:{self.repository_label} {{id: $repository_id}})-[:{self.contains_file_rel}]->(file:{self.file_label})
        RETURN file
        """

    @property
    def DELETE_ORGANIZATION_FILES(self):
        """Delete all files for an organization."""
        return f"""
        MATCH (f:{self.file_label} {{organisation_id: $organisation_id}})
        DETACH DELETE f
        """

class GitHubRelationshipQueries:
    """Queries for managing relationships between GitHub entities and organizational entities."""

    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.agent_label = agent_schema.get_node_labels()[0] if agent_schema else "Agent"
        self.department_label = organisation_schema.get_node_labels()[2]  # Department
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.has_access_rel = "CONTRIBUTES_TO"  # From schema relationships
        self.belongs_to_rel = organisation_schema.get_relationship_types()[2]  # BELONGS_TO

    @property
    def CREATE_AGENT_REPOSITORY_RELATIONSHIP(self):
        """Create agent-repository relationship."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id}})
        WHERE a.organisation_id = r.organisation_id
        MERGE (a)-[:{self.has_access_rel}]->(r)
        """

    @property
    def CREATE_DEPARTMENT_REPOSITORY_RELATIONSHIP(self):
        """Create department-repository relationship."""
        return f"""
        MATCH (d:{self.department_label} {{id: $department_id}})
        MATCH (r:{self.repository_label} {{id: $repository_id}})
        WHERE d.organisation_id = r.organisation_id
        MERGE (d)-[:{self.has_access_rel}]->(r)
        """

class GitHubServiceAccountQueries:
    """Queries for GitHub service account operations."""

    def __init__(self):
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.github_org_label = github_schema.get_node_labels()[2]  # GitHubOrganization

    @property
    def GET_ORGANIZATION_REPOSITORIES(self):
        """Get all repositories for an organization."""
        return f"""
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        RETURN r
        ORDER BY r.name
        """

    @property
    def GET_ORGANIZATION_USERS(self):
        """Get all users for an organization."""
        return f"""
        MATCH (u:{self.github_user_label} {{organisation_id: $organisation_id}})
        RETURN u
        ORDER BY u.login
        """


class GitHubSyncQueries:
    """Queries for GitHub sync operations."""

    def __init__(self):
        self.repository_label = github_schema.get_node_labels()[0]  # GitHubRepository
        self.github_user_label = github_schema.get_node_labels()[1]  # GitHubUser
        self.file_label = github_schema.get_node_labels()[3]  # GitHubCodeFile
        self.issue_label = github_schema.get_node_labels()[4]  # GitHubIssue
        self.pr_label = github_schema.get_node_labels()[5]  # GitHubPullRequest
        self.commit_label = github_schema.get_node_labels()[6]  # GitHubCommit

    @property
    def GET_REPOSITORIES_MODIFIED_SINCE(self):
        """Get repositories modified since a specific date."""
        return f"""
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        WHERE r.updated_at > $since_date
        RETURN r
        ORDER BY r.updated_at DESC
        LIMIT $limit
        """

    @property
    def UPDATE_LAST_SYNC_TIME(self):
        """Update the last sync time for an organisation."""
        return """
        MERGE (o:Organisation {id: $organisation_id})-[:HAS_SOURCE]->(s:Source {type: 'github'})
        ON CREATE SET s.last_sync_time = $sync_time, s.created_at = $sync_time
        ON MATCH SET s.last_sync_time = $sync_time, s.updated_at = $sync_time
        RETURN s
        """

    @property
    def GET_SYNC_STATISTICS(self):
        """Get sync statistics for an organisation."""
        return f"""
        MATCH (r:{self.repository_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (f:{self.file_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (i:{self.issue_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (pr:{self.pr_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (c:{self.commit_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (u:{self.github_user_label} {{organisation_id: $organisation_id}})
        OPTIONAL MATCH (o:Organisation {{id: $organisation_id}})-[:HAS_SOURCE]->(s:Source {{type: 'github'}})
        RETURN
            count(DISTINCT r) as repositories_count,
            count(DISTINCT f) as files_count,
            count(DISTINCT i) as issues_count,
            count(DISTINCT pr) as pull_requests_count,
            count(DISTINCT c) as commits_count,
            count(DISTINCT u) as users_count,
            s.last_sync_time as last_sync_time
        """

class GitHubMetadataQueries:
    """Queries for managing file and repository metadata."""

    def __init__(self):
        self.file_label = github_schema.get_node_labels()[3]  # GitHubCodeFile

    @property
    def UPDATE_FILE_VECTORIZATION_METADATA(self):
        return f"""
        MATCH (f:{self.file_label} {{id: $file_id}})
        SET f.vectorized_at = $vectorized_at,
            f.last_vectorized_modified_time = $modified_time
        """

    @property
    def CHECK_FILE_VECTORIZATION_STATUS(self):
        return f"""
        MATCH (f:{self.file_label} {{id: $file_id}})
        RETURN f.vector_id, f.vectorized_at, f.last_vectorized_modified_time
        """
